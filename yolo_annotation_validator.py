#!/usr/bin/env python3
"""
YOLO Pose 标注数据一致性检测脚本
用于检测YOLO pose格式标注文件中关键点数量的一致性和其他潜在问题
"""

import os
import glob
from collections import defaultdict, Counter
import argparse
from pathlib import Path


class YOLOPoseValidator:
    def __init__(self, dataset_path):
        self.dataset_path = Path(dataset_path)
        self.train_labels_path = self.dataset_path / "labels" / "train"
        self.val_labels_path = self.dataset_path / "labels" / "val"
        self.train_images_path = self.dataset_path / "images" / "train"
        self.val_images_path = self.dataset_path / "images" / "val"
        
        self.errors = []
        self.warnings = []
        self.stats = defaultdict(list)
        
    def validate_file_format(self, file_path):
        """验证单个标注文件的格式"""
        errors = []
        warnings = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 移除空行
            lines = [line.strip() for line in lines if line.strip()]
            
            if not lines:
                warnings.append(f"空文件: {file_path}")
                return errors, warnings, None
            
            file_stats = {
                'num_objects': len(lines),
                'keypoints_per_object': [],
                'classes': [],
                'bbox_issues': [],
                'keypoint_issues': []
            }
            
            for line_num, line in enumerate(lines, 1):
                parts = line.split()
                
                if len(parts) < 5:
                    errors.append(f"{file_path}:{line_num} - 格式错误: 至少需要5个值 (class_id + bbox), 实际: {len(parts)}")
                    continue
                
                try:
                    # 解析类别ID
                    class_id = int(parts[0])
                    file_stats['classes'].append(class_id)
                    
                    # 解析边界框
                    bbox = [float(x) for x in parts[1:5]]
                    x_center, y_center, width, height = bbox
                    
                    # 检查边界框范围
                    if not (0 <= x_center <= 1 and 0 <= y_center <= 1):
                        file_stats['bbox_issues'].append(f"line {line_num}: 中心点超出范围 ({x_center:.3f}, {y_center:.3f})")
                    
                    if not (0 < width <= 1 and 0 < height <= 1):
                        file_stats['bbox_issues'].append(f"line {line_num}: 尺寸异常 ({width:.3f}, {height:.3f})")
                    
                    # 解析关键点
                    keypoint_data = parts[5:]
                    if len(keypoint_data) % 3 != 0:
                        errors.append(f"{file_path}:{line_num} - 关键点数据错误: 应该是3的倍数, 实际: {len(keypoint_data)}")
                        continue
                    
                    num_keypoints = len(keypoint_data) // 3
                    file_stats['keypoints_per_object'].append(num_keypoints)
                    
                    # 检查关键点格式
                    for i in range(0, len(keypoint_data), 3):
                        try:
                            x = float(keypoint_data[i])
                            y = float(keypoint_data[i + 1])
                            v = int(keypoint_data[i + 2])
                            
                            # 检查可见性标志
                            if v not in [0, 1, 2]:
                                file_stats['keypoint_issues'].append(f"line {line_num}: 可见性标志错误 {v} (应该是0,1,2)")
                            
                            # 检查坐标范围 (只对可见点检查)
                            if v > 0:
                                if not (0 <= x <= 1 and 0 <= y <= 1):
                                    file_stats['keypoint_issues'].append(f"line {line_num}: 关键点坐标超出范围 ({x:.3f}, {y:.3f})")
                        
                        except ValueError as e:
                            errors.append(f"{file_path}:{line_num} - 关键点解析错误: {e}")
                
                except ValueError as e:
                    errors.append(f"{file_path}:{line_num} - 数值解析错误: {e}")
            
            return errors, warnings, file_stats
            
        except Exception as e:
            errors.append(f"读取文件失败 {file_path}: {e}")
            return errors, warnings, None
    
    def check_image_label_pairs(self):
        """检查图像和标注文件的配对"""
        missing_labels = []
        missing_images = []
        
        # 检查训练集
        if self.train_images_path.exists():
            for img_file in self.train_images_path.glob("*"):
                if img_file.suffix.lower() in ['.jpg', '.jpeg', '.png', '.bmp']:
                    label_file = self.train_labels_path / f"{img_file.stem}.txt"
                    if not label_file.exists():
                        missing_labels.append(str(label_file))
        
        if self.train_labels_path.exists():
            for label_file in self.train_labels_path.glob("*.txt"):
                # 查找对应的图像文件
                found = False
                for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.JPG', '.JPEG', '.PNG', '.BMP']:
                    img_file = self.train_images_path / f"{label_file.stem}{ext}"
                    if img_file.exists():
                        found = True
                        break
                if not found:
                    missing_images.append(f"{label_file.stem}")
        
        return missing_labels, missing_images
    
    def validate_dataset(self):
        """验证整个数据集"""
        print("开始验证YOLO Pose数据集...")
        print(f"数据集路径: {self.dataset_path}")
        
        all_keypoint_counts = []
        all_class_ids = []
        file_count = 0
        
        # 检查图像标注配对
        missing_labels, missing_images = self.check_image_label_pairs()
        
        if missing_labels:
            print(f"\n⚠️  缺少标注文件 ({len(missing_labels)}个):")
            for label in missing_labels[:10]:  # 只显示前10个
                print(f"  - {label}")
            if len(missing_labels) > 10:
                print(f"  ... 还有 {len(missing_labels) - 10} 个")
        
        if missing_images:
            print(f"\n⚠️  缺少图像文件 ({len(missing_images)}个):")
            for img in missing_images[:10]:
                print(f"  - {img}")
            if len(missing_images) > 10:
                print(f"  ... 还有 {len(missing_images) - 10} 个")
        
        # 验证标注文件
        for split in ['train', 'val']:
            labels_path = self.dataset_path / "labels" / split
            if not labels_path.exists():
                print(f"\n⚠️  {split} 标注目录不存在: {labels_path}")
                continue
            
            print(f"\n验证 {split} 集...")
            label_files = list(labels_path.glob("*.txt"))
            
            for label_file in label_files:
                file_count += 1
                errors, warnings, stats = self.validate_file_format(label_file)
                
                self.errors.extend(errors)
                self.warnings.extend(warnings)
                
                if stats:
                    all_class_ids.extend(stats['classes'])
                    all_keypoint_counts.extend(stats['keypoints_per_object'])
                    
                    if stats['bbox_issues']:
                        self.warnings.extend([f"{label_file}: {issue}" for issue in stats['bbox_issues']])
                    
                    if stats['keypoint_issues']:
                        self.warnings.extend([f"{label_file}: {issue}" for issue in stats['keypoint_issues']])
        
        # 统计分析
        print(f"\n📊 数据集统计:")
        print(f"  总文件数: {file_count}")
        print(f"  总对象数: {len(all_keypoint_counts)}")
        
        if all_class_ids:
            class_counter = Counter(all_class_ids)
            print(f"  类别分布: {dict(class_counter)}")
        
        if all_keypoint_counts:
            keypoint_counter = Counter(all_keypoint_counts)
            print(f"  关键点数量分布: {dict(keypoint_counter)}")
            
            # 关键点一致性检查
            unique_keypoint_counts = set(all_keypoint_counts)
            if len(unique_keypoint_counts) > 1:
                print(f"\n❌ 关键点数量不一致!")
                print(f"   发现的关键点数量: {sorted(unique_keypoint_counts)}")
                print(f"   这可能是训练失败的原因!")
                
                # 详细分析哪些文件有问题
                print(f"\n🔍 详细分析:")
                most_common_count = keypoint_counter.most_common(1)[0][0]
                print(f"   最常见的关键点数量: {most_common_count} (出现{keypoint_counter[most_common_count]}次)")
                
                problem_files = []
                for split in ['train', 'val']:
                    labels_path = self.dataset_path / "labels" / split
                    if labels_path.exists():
                        for label_file in labels_path.glob("*.txt"):
                            _, _, stats = self.validate_file_format(label_file)
                            if stats and stats['keypoints_per_object']:
                                for count in stats['keypoints_per_object']:
                                    if count != most_common_count:
                                        problem_files.append((label_file, count))
                
                if problem_files:
                    print(f"   异常文件 ({len(problem_files)}个):")
                    for file_path, count in problem_files[:10]:
                        print(f"     - {file_path.name}: {count}个关键点")
                    if len(problem_files) > 10:
                        print(f"     ... 还有 {len(problem_files) - 10} 个")
            else:
                print(f"\n✅ 关键点数量一致: 每个对象都有 {list(unique_keypoint_counts)[0]} 个关键点")
        
        # 错误和警告汇总
        if self.errors:
            print(f"\n❌ 发现 {len(self.errors)} 个错误:")
            for error in self.errors[:10]:
                print(f"  - {error}")
            if len(self.errors) > 10:
                print(f"  ... 还有 {len(self.errors) - 10} 个错误")
        
        if self.warnings:
            print(f"\n⚠️  发现 {len(self.warnings)} 个警告:")
            for warning in self.warnings[:10]:
                print(f"  - {warning}")
            if len(self.warnings) > 10:
                print(f"  ... 还有 {len(self.warnings) - 10} 个警告")
        
        if not self.errors and not self.warnings:
            print(f"\n✅ 数据集验证通过!")
        
        return len(self.errors) == 0


def main():
    parser = argparse.ArgumentParser(description='YOLO Pose数据集一致性验证工具')
    parser.add_argument('dataset_path', nargs='?', default='yolo_dataset', 
                       help='YOLO数据集路径 (默认: yolo_dataset)')
    
    args = parser.parse_args()
    
    validator = YOLOPoseValidator(args.dataset_path)
    success = validator.validate_dataset()
    
    if not success:
        print(f"\n💡 建议:")
        print(f"  1. 修复上述错误后重新训练")
        print(f"  2. 确保所有标注文件的关键点数量一致")
        print(f"  3. 检查边界框和关键点坐标是否在[0,1]范围内")
        print(f"  4. 确保图像和标注文件一一对应")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
