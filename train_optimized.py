from ultralytics import YOLO
import os
import torch


def train_yolo():
    # 检查CUDA环境
    print("=" * 50)
    print("环境检查:")
    print(f"CUDA版本: {torch.version.cuda}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"GPU名称: {torch.cuda.get_device_name(0)}")
        print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    print("=" * 50)
    
    # 初始化模型
    model = YOLO("yolo12n.pt")  # 使用nano版本，适合小数据集
    
    # 针对小数据集优化的训练参数
    results = model.train(
        data="data.yaml",           # 数据配置文件
        epochs=40,                  # 减少epochs，避免过拟合
        imgsz=320,                  # 减小输入尺寸，提高训练速度
        device=0,                   # 使用GPU
        batch=4,                    # 小batch size，适合小数据集
        workers=4,                  # 减少worker数量
        patience=15,                # 早停耐心值，15个epoch无改善则停止
        save_period=10,             # 每10个epoch保存一次
        
        # 数据增强参数（针对小数据集）
        hsv_h=0.015,               # 色调增强
        hsv_s=0.7,                 # 饱和度增强
        hsv_v=0.4,                 # 明度增强
        degrees=10,                # 旋转角度
        translate=0.1,             # 平移
        scale=0.2,                 # 缩放
        shear=2,                   # 剪切
        perspective=0.0001,        # 透视变换
        flipud=0.0,                # 上下翻转（手掌检测不建议）
        fliplr=0.5,                # 左右翻转
        mosaic=0.8,                # Mosaic增强概率
        mixup=0.1,                 # Mixup增强概率
        copy_paste=0.1,            # Copy-paste增强
        
        # 优化器设置
        optimizer='AdamW',          # 使用AdamW优化器
        lr0=0.001,                 # 初始学习率（稍微降低）
        lrf=0.01,                  # 最终学习率因子
        momentum=0.937,            # 动量
        weight_decay=0.0005,       # 权重衰减
        warmup_epochs=3,           # 预热epochs
        warmup_momentum=0.8,       # 预热动量
        warmup_bias_lr=0.1,        # 预热偏置学习率
        
        # 其他设置
        box=7.5,                   # box loss权重
        cls=0.5,                   # 分类loss权重
        dfl=1.5,                   # DFL loss权重
        pose=12.0,                 # 姿态loss权重（如果使用姿态检测）
        kobj=2.0,                  # 关键点obj loss权重
        
        # 验证设置
        val=True,                  # 启用验证
        plots=True,                # 生成训练图表
        save=True,                 # 保存检查点
        save_txt=False,            # 不保存txt结果
        save_conf=False,           # 不保存置信度
        save_crop=False,           # 不保存裁剪图像
        
        # 项目设置
        project='runs/train',      # 项目目录
        name='hand_detection',     # 实验名称
        exist_ok=True,             # 允许覆盖现有实验
        
        # 其他
        verbose=True,              # 详细输出
        seed=42,                   # 随机种子
        deterministic=True,        # 确定性训练
        single_cls=True,           # 单类别检测
        rect=False,                # 不使用矩形训练
        cos_lr=True,               # 使用余弦学习率调度
        close_mosaic=10,           # 最后10个epoch关闭mosaic
        resume=False,              # 不恢复训练
        amp=True,                  # 自动混合精度
        fraction=1.0,              # 使用全部数据
        profile=False,             # 不进行性能分析
        freeze=None,               # 不冻结层
        
        # 多尺度训练
        multi_scale=True,          # 启用多尺度训练
        overlap_mask=True,         # 重叠mask
        mask_ratio=4,              # mask比率
        dropout=0.0,               # dropout率
        
        # NMS设置
        iou=0.7,                   # NMS IoU阈值
        max_det=300,               # 最大检测数量
    )
    
    # 训练完成后的操作
    print("=" * 50)
    print("训练完成!")
    print(f"最佳模型保存在: {results.save_dir}/weights/best.pt")
    print(f"最后模型保存在: {results.save_dir}/weights/last.pt")
    print("=" * 50)
    
    # 可选：进行模型验证
    print("开始模型验证...")
    metrics = model.val()
    print(f"验证结果:")
    print(f"mAP50: {metrics.box.map50:.4f}")
    print(f"mAP50-95: {metrics.box.map:.4f}")
    
    return results


def train_simple():
    """简化版训练函数，使用最基本的参数"""
    print("使用简化参数进行训练...")
    
    model = YOLO("yolo12n.pt")
    
    results = model.train(
        data="data.yaml",
        epochs=50,                 # 较少的epochs
        imgsz=416,                 # 较小的输入尺寸
        device=0,
        batch=2,                   # 很小的batch size
        workers=2,                 # 较少的workers
        patience=10,               # 早停
        lr0=0.001,                 # 较小的学习率
        save_period=5,             # 每5个epoch保存
        project='runs/train',
        name='hand_simple',
        exist_ok=True,
        single_cls=True,           # 单类别
        verbose=True
    )
    
    return results


if __name__ == '__main__':
    # 选择训练模式
    mode = input("选择训练模式 (1: 优化参数, 2: 简化参数): ").strip()
    
    if mode == "2":
        train_simple()
    else:
        train_yolo()
