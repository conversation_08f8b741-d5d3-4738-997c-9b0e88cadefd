import os
import cv2
from PIL import Image


def rotate_images(directory):
    """
    将指定目录下所有以Hand_开头的图片旋转180度
    """
    # 确保目录存在
    if not os.path.exists(directory):
        print(f"目录 '{directory}' 不存在")
        return

    # 获取所有以Hand_开头的图片文件
    image_files = [f for f in os.listdir(directory)
                   if f.startswith("Hand_") and
                   (f.lower().endswith('.jpg') or f.lower().endswith('.jpeg') or
                    f.lower().endswith('.png') or f.lower().endswith('.bmp'))]

    if not image_files:
        print(f"在 '{directory}' 目录中没有找到以Hand_开头的图片")
        return

    # 创建保存旋转后图片的目录
    rotated_dir = os.path.join(directory, "rotated")
    if not os.path.exists(rotated_dir):
        os.makedirs(rotated_dir)

    # 旋转每张图片
    for image_file in image_files:
        image_path = os.path.join(directory, image_file)
        try:
            # 使用PIL打开图片并旋转
            img = Image.open(image_path)
            rotated_img = img.rotate(180)

            # 保存旋转后的图片
            output_path = os.path.join(rotated_dir, f"rotated_{image_file}")
            rotated_img.save(output_path)
            print(f"已旋转并保存: {output_path}")
        except Exception as e:
            print(f"处理图片 {image_file} 时出错: {e}")


if __name__ == "__main__":
    rotate_images("手掌")
    print("处理完成")