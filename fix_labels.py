import os
import glob

def fix_label_format(label_file_path):
    """
    修复YOLO关键点检测标签格式
    从当前格式转换为正确的26列格式
    """
    try:
        with open(label_file_path, 'r') as f:
            lines = f.readlines()
        
        fixed_lines = []
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            parts = line.split()
            if len(parts) < 5:  # 至少需要类别ID和边界框信息
                print(f"跳过格式错误的行: {line}")
                continue
            
            # 解析当前格式
            class_id = parts[0]  # 类别ID
            
            # 假设前5个数字是: class_id, center_x, center_y, width, height
            if len(parts) >= 5:
                center_x = parts[1]
                center_y = parts[2] 
                width = parts[3]
                height = parts[4]
                
                # 解析关键点信息
                keypoints = []
                remaining_parts = parts[5:]
                
                # 当前格式似乎是: x1 y1 v1 x2 y2 v2 ... 但格式不规范
                # 我们需要重新组织为标准格式
                
                # 从剩余部分提取关键点，跳过可见性标记
                kpt_coords = []
                i = 0
                while i < len(remaining_parts) and len(kpt_coords) < 14:  # 7个关键点 * 2坐标
                    try:
                        val = float(remaining_parts[i])
                        # 如果值是2，跳过（这是可见性标记）
                        if val == 2.0:
                            i += 1
                            continue
                        # 如果值是0且下一个也是0，这可能是无效关键点
                        if val == 0.0 and i + 1 < len(remaining_parts) and float(remaining_parts[i + 1]) == 0.0:
                            kpt_coords.extend([0.0, 0.0])
                            i += 2
                            continue
                        kpt_coords.append(val)
                        i += 1
                    except ValueError:
                        i += 1
                        continue
                
                # 确保我们有7个关键点（14个坐标）
                while len(kpt_coords) < 14:
                    kpt_coords.extend([0.0, 0.0])
                
                # 构建标准格式：class_id center_x center_y width height kpt1_x kpt1_y kpt1_v ...
                fixed_line = f"{class_id} {center_x} {center_y} {width} {height}"
                
                # 添加7个关键点，每个关键点3个值（x, y, visibility）
                for i in range(0, min(14, len(kpt_coords)), 2):
                    x = kpt_coords[i] if i < len(kpt_coords) else 0.0
                    y = kpt_coords[i + 1] if i + 1 < len(kpt_coords) else 0.0
                    # 如果坐标都是0，设置为不可见(0)，否则设置为可见(2)
                    visibility = 0 if (x == 0.0 and y == 0.0) else 2
                    fixed_line += f" {x} {y} {visibility}"
                
                fixed_lines.append(fixed_line + "\n")
            
        # 写回修复后的内容
        with open(label_file_path, 'w') as f:
            f.writelines(fixed_lines)
            
        print(f"修复完成: {label_file_path}")
        return True
        
    except Exception as e:
        print(f"修复失败 {label_file_path}: {e}")
        return False

def main():
    """修复所有标签文件"""
    # 修复训练集标签
    train_labels_dir = "yolo_dataset/labels/train"
    val_labels_dir = "yolo_dataset/labels/val"
    
    print("开始修复训练集标签...")
    train_files = glob.glob(os.path.join(train_labels_dir, "*.txt"))
    for label_file in train_files:
        fix_label_format(label_file)
    
    print("\n开始修复验证集标签...")
    val_files = glob.glob(os.path.join(val_labels_dir, "*.txt"))
    for label_file in val_files:
        fix_label_format(label_file)
    
    print(f"\n修复完成！")
    print(f"训练集: {len(train_files)} 个文件")
    print(f"验证集: {len(val_files)} 个文件")
    
    # 删除缓存文件，强制重新生成
    cache_file = "yolo_dataset/labels/train.cache"
    if os.path.exists(cache_file):
        os.remove(cache_file)
        print("已删除缓存文件，将重新生成")

if __name__ == "__main__":
    main()
