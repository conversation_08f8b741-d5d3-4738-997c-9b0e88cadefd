import os
import glob

def convert_keypoint_to_bbox_format():
    """
    将关键点格式的标签转换为YOLO目标检测格式
    原格式：class_id x_center y_center width height [keypoints...]
    新格式：class_id x_center y_center width height
    """
    
    # 处理训练集标签
    train_labels_path = "yolo_dataset/labels/train"
    val_labels_path = "yolo_dataset/labels/val"
    
    for labels_path in [train_labels_path, val_labels_path]:
        print(f"处理 {labels_path} 中的标签文件...")
        
        # 获取所有txt文件
        txt_files = glob.glob(os.path.join(labels_path, "*.txt"))
        
        for txt_file in txt_files:
            if txt_file.endswith('.cache'):
                continue
                
            print(f"处理文件: {txt_file}")
            
            # 读取原文件
            with open(txt_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 转换格式
            new_lines = []
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                    
                parts = line.split()
                if len(parts) >= 5:
                    # 只保留前5个部分：class_id x_center y_center width height
                    class_id = parts[0]
                    x_center = parts[1]
                    y_center = parts[2]
                    width = parts[3]
                    height = parts[4]
                    
                    new_line = f"{class_id} {x_center} {y_center} {width} {height}\n"
                    new_lines.append(new_line)
            
            # 写入新文件
            with open(txt_file, 'w', encoding='utf-8') as f:
                f.writelines(new_lines)
            
            print(f"已转换: {txt_file}")
    
    print("标签格式转换完成！")

def remove_cache_files():
    """删除缓存文件，让YOLO重新生成"""
    cache_files = [
        "yolo_dataset/labels/train.cache",
        "yolo_dataset/labels/val.cache"
    ]
    
    for cache_file in cache_files:
        if os.path.exists(cache_file):
            os.remove(cache_file)
            print(f"已删除缓存文件: {cache_file}")

if __name__ == "__main__":
    print("开始转换标签格式...")
    convert_keypoint_to_bbox_format()
    
    print("\n删除缓存文件...")
    remove_cache_files()
    
    print("\n转换完成！现在可以重新运行训练脚本了。")
