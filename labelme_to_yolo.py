import os
import json

def convert_labelme_to_yolo(labelme_dir, output_dir):
    """
    将labelme格式转换为YOLO关键点检测格式
    
    Args:
        labelme_dir: labelme标注文件所在目录
        output_dir: 输出YOLO格式文件的目录
    """
    # 确保输出目录存在
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 定义类别映射
    bbox_class = {"hand": 0}  # 框的类别，这里只有hand一类
    
    # 定义关键点类别顺序（根据文件中观察到的关键点）
    keypoint_class = [
        "zhongchong", "shaoze", "qiangu", "sifeng", 
        "laogong", "shaofu", "wangu", "shixuan"
    ]
    
    # 获取所有json文件
    json_files = [f for f in os.listdir(labelme_dir) if f.endswith('.json')]
    
    for json_file in json_files:
        labelme_path = os.path.join(labelme_dir, json_file)
        
        # 读取labelme标注文件
        with open(labelme_path, 'r', encoding='utf-8') as f:
            labelme = json.load(f)
        
        # 获取图像尺寸
        img_width = labelme.get('imageWidth', 0)
        img_height = labelme.get('imageHeight', 0)
        
        if img_width == 0 or img_height == 0:
            print(f"警告: {json_file} 中没有找到有效的图像尺寸信息")
            continue
        
        # 创建输出文件路径
        yolo_txt_path = os.path.join(output_dir, os.path.splitext(json_file)[0] + '.txt')
        
        with open(yolo_txt_path, 'w', encoding='utf-8') as f:
            for each_ann in labelme['shapes']:  # 遍历每个标注
                if each_ann['shape_type'] == 'rectangle':  # 如果遇到框
                    yolo_str = ''
                    
                    ## 框的信息
                    # 框的类别 ID
                    bbox_class_id = bbox_class[each_ann['label']]
                    yolo_str += '{} '.format(bbox_class_id)
                    
                    # 左上角和右下角的 XY 像素坐标
                    bbox_top_left_x = int(min(each_ann['points'][0][0], each_ann['points'][1][0]))
                    bbox_bottom_right_x = int(max(each_ann['points'][0][0], each_ann['points'][1][0]))
                    bbox_top_left_y = int(min(each_ann['points'][0][1], each_ann['points'][1][1]))
                    bbox_bottom_right_y = int(max(each_ann['points'][0][1], each_ann['points'][1][1]))
                    
                    # 框中心点的 XY 像素坐标
                    bbox_center_x = int((bbox_top_left_x + bbox_bottom_right_x) / 2)
                    bbox_center_y = int((bbox_top_left_y + bbox_bottom_right_y) / 2)
                    
                    # 框宽度和高度
                    bbox_width = bbox_bottom_right_x - bbox_top_left_x
                    bbox_height = bbox_bottom_right_y - bbox_top_left_y
                    
                    # 框中心点归一化坐标和归一化宽高
                    bbox_center_x_norm = bbox_center_x / img_width
                    bbox_center_y_norm = bbox_center_y / img_height
                    bbox_width_norm = bbox_width / img_width
                    bbox_height_norm = bbox_height / img_height
                    
                    yolo_str += '{:.5f} {:.5f} {:.5f} {:.5f} '.format(
                        bbox_center_x_norm, bbox_center_y_norm, bbox_width_norm, bbox_height_norm
                    )
                    
                    ## 找到该框中所有关键点，存在字典 bbox_keypoints_dict 中
                    bbox_keypoints_dict = {}
                    for kp_ann in labelme['shapes']:  # 遍历所有标注
                        if kp_ann['shape_type'] == 'point':  # 筛选出关键点标注
                            # 关键点XY坐标、类别
                            x = int(kp_ann['points'][0][0])
                            y = int(kp_ann['points'][0][1])
                            label = kp_ann['label']
                            if (x > bbox_top_left_x and x < bbox_bottom_right_x and 
                                y < bbox_bottom_right_y and y > bbox_top_left_y):  # 筛选出在该个体框中的关键点
                                bbox_keypoints_dict[label] = [x, y]
                    
                    ## 把关键点按顺序排好
                    for each_class in keypoint_class:  # 遍历每一类关键点
                        if each_class in bbox_keypoints_dict:
                            keypoint_x_norm = bbox_keypoints_dict[each_class][0] / img_width
                            keypoint_y_norm = bbox_keypoints_dict[each_class][1] / img_height
                            yolo_str += '{:.5f} {:.5f} {} '.format(keypoint_x_norm, keypoint_y_norm, 2)  # 2-可见不遮挡
                        else:  # 不存在的点，一律为0
                            yolo_str += '0 0 0 '
                    
                    # 写入 txt 文件中
                    f.write(yolo_str.strip() + '\n')
        
        print('{} --> {} 转换完成'.format(labelme_path, yolo_txt_path))

if __name__ == "__main__":
    # 设置输入和输出目录
    labelme_dir = "手掌"
    output_dir = "手掌/yolo_labels"
    
    convert_labelme_to_yolo(labelme_dir, output_dir)
    print("所有文件转换完成！")