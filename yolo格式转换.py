import json
import os
from PIL import Image
import glob


def labelme_to_yolo_keypoints(json_file_path, output_dir, class_names=['hand']):
    """
    将LabelMe格式的JSON文件转换为YOLO关键点检测格式

    Args:
        json_file_path: LabelMe JSON文件路径
        output_dir: 输出目录
        class_names: 类别名称列表，默认为['hand']
    """

    # 定义关键点顺序映射 - 根据你的JSON文件定义
    keypoint_order = [
        'zhongchong',  # 0
        'shaoze',  # 1
        'qiangu',  # 2
        'sifeng',  # 3
        'laogong',  # 4
        'shaofu',  # 5
        'wangu'  # 6
    ]

    with open(json_file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    # 获取图像尺寸
    image_path = data['imagePath']

    # 尝试从同目录找到图像文件
    json_dir = os.path.dirname(json_file_path)
    full_image_path = os.path.join(json_dir, image_path)

    if not os.path.exists(full_image_path):
        print(f"警告: 找不到图像文件 {full_image_path}")
        return False

    try:
        with Image.open(full_image_path) as img:
            img_width, img_height = img.size
    except Exception as e:
        print(f"错误: 无法读取图像 {full_image_path}: {e}")
        return False

    # 解析shapes
    rectangles = []  # 边界框
    keypoints_dict = {}  # 关键点字典

    for shape in data['shapes']:
        if shape['shape_type'] == 'rectangle' and shape['label'] in class_names:
            # 处理边界框
            points = shape['points']
            x1, y1 = points[0]
            x2, y2 = points[1]

            # 确保坐标顺序正确
            x_min = min(x1, x2)
            x_max = max(x1, x2)
            y_min = min(y1, y2)
            y_max = max(y1, y2)

            # 转换为YOLO格式 (中心点坐标 + 宽高)
            x_center = (x_min + x_max) / 2.0
            y_center = (y_min + y_max) / 2.0
            width = x_max - x_min
            height = y_max - y_min

            # 归一化
            x_center /= img_width
            y_center /= img_height
            width /= img_width
            height /= img_height

            class_id = class_names.index(shape['label'])
            rectangles.append({
                'class_id': class_id,
                'x_center': x_center,
                'y_center': y_center,
                'width': width,
                'height': height
            })

        elif shape['shape_type'] == 'point':
            # 处理关键点
            point = shape['points'][0]  # point类型只有一个坐标
            x, y = point

            # 归一化坐标
            x_norm = x / img_width
            y_norm = y / img_height

            keypoints_dict[shape['label']] = {
                'x': x_norm,
                'y': y_norm,
                'visibility': 2  # 2表示可见
            }

    # 生成YOLO标注文件
    if rectangles:
        output_file = os.path.join(output_dir,
                                   os.path.splitext(os.path.basename(json_file_path))[0] + '.txt')

        with open(output_file, 'w') as f:
            for rect in rectangles:
                # 开始构建YOLO格式字符串
                line = f"{rect['class_id']} {rect['x_center']:.6f} {rect['y_center']:.6f} {rect['width']:.6f} {rect['height']:.6f}"

                # 按照预定义顺序添加关键点
                for keypoint_name in keypoint_order:
                    if keypoint_name in keypoints_dict:
                        kp = keypoints_dict[keypoint_name]
                        line += f" {kp['x']:.6f} {kp['y']:.6f} {kp['visibility']}"
                    else:
                        # 如果某个关键点不存在，用0 0 0表示
                        line += " 0.0 0.0 0"

                f.write(line + '\n')

        print(f"转换成功: {json_file_path} -> {output_file}")
        return True
    else:
        print(f"警告: {json_file_path} 中没有找到有效的边界框")
        return False


def batch_convert_labelme_to_yolo(input_dir, output_dir, class_names=['hand']):
    """
    批量转换LabelMe JSON文件到YOLO格式

    Args:
        input_dir: 包含JSON文件的输入目录
        output_dir: 输出目录
        class_names: 类别名称列表
    """

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 查找所有JSON文件
    json_files = glob.glob(os.path.join(input_dir, '*.json'))

    if not json_files:
        print(f"在 {input_dir} 中没有找到JSON文件")
        return

    success_count = 0
    total_count = len(json_files)

    print(f"找到 {total_count} 个JSON文件，开始转换...")

    for json_file in json_files:
        try:
            if labelme_to_yolo_keypoints(json_file, output_dir, class_names):
                success_count += 1
        except Exception as e:
            print(f"转换失败 {json_file}: {e}")

    print(f"\n转换完成: {success_count}/{total_count} 个文件转换成功")


def create_dataset_yaml(output_dir, class_names=['hand'], num_keypoints=7):
    """
    创建YOLO数据集配置文件

    Args:
        output_dir: 输出目录
        class_names: 类别名称列表
        num_keypoints: 关键点数量
    """

    keypoint_names = [
        'zhongchong', 'shaoze', 'qiangu', 'sifeng',
        'laogong', 'shaofu', 'wangu'
    ]

    # 创建关键点连接关系 (可根据实际手部结构调整)
    skeleton = [
        [0, 1], [1, 2], [2, 3], [3, 4], [4, 5], [5, 6]  # 示例连接
    ]

    yaml_content = f"""# YOLO关键点检测数据集配置文件
path: {os.path.abspath(output_dir)}  # 数据集根目录
train: images/train  # 训练图像路径
val: images/val      # 验证图像路径

# 类别
nc: {len(class_names)}  # 类别数量
names: {class_names}    # 类别名称

# 关键点配置
kpt_shape: [{num_keypoints}, 3]  # 关键点数量和坐标维度 (x, y, visibility)
kpt_names: {keypoint_names}
skeleton: {skeleton}
"""

    yaml_path = os.path.join(output_dir, 'data.yaml')
    with open(yaml_path, 'w', encoding='utf-8') as f:
        f.write(yaml_content)

    print(f"数据集配置文件已创建: {yaml_path}")


# 使用示例
if __name__ == "__main__":
    # 设置路径
    input_directory = r"E:\xuewei\手掌"  # LabelMe JSON文件所在目录
    output_directory = r"E:\xuewei\手掌\yolo_labels"  # YOLO格式文件输出目录

    # 类别名称
    class_names = ['hand']

    # 批量转换
    batch_convert_labelme_to_yolo(input_directory, output_directory, class_names)

    # 创建数据集配置文件
    create_dataset_yaml(r"E:\xuewei\yolo_dataset", class_names, num_keypoints=7)

    print("\n使用说明：")
    print("1. 确保图像文件和JSON文件在同一目录下")
    print("2. 修改脚本中的路径设置")
    print("3. 根据需要调整关键点顺序和连接关系")
    print("4. 运行脚本进行批量转换")