import os
import glob

def convert_keypoint_to_detection(label_file):
    """
    将关键点格式转换为目标检测格式
    输入格式：class_id x_center y_center width height kpt1_x kpt1_y vis1 kpt2_x kpt2_y vis2 ...
    输出格式：class_id x_center y_center width height
    """
    with open(label_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    converted_lines = []
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        parts = line.split()
        if len(parts) >= 5:
            # 只保留前5个部分：class_id x_center y_center width height
            class_id = parts[0]
            x_center = float(parts[1])
            y_center = float(parts[2])
            width = float(parts[3])
            height = float(parts[4])
            
            # 检查坐标是否在有效范围内
            if (0 <= x_center <= 1 and 0 <= y_center <= 1 and 
                0 < width <= 1 and 0 < height <= 1):
                converted_line = f"{class_id} {x_center} {y_center} {width} {height}"
                converted_lines.append(converted_line)
            else:
                print(f"跳过无效坐标: {label_file} - 坐标超出范围")
    
    return converted_lines

def process_labels_directory(labels_dir):
    """处理标签目录中的所有txt文件"""
    txt_files = glob.glob(os.path.join(labels_dir, "*.txt"))
    
    for txt_file in txt_files:
        print(f"处理文件: {txt_file}")
        converted_lines = convert_keypoint_to_detection(txt_file)
        
        if converted_lines:
            # 写回转换后的内容
            with open(txt_file, 'w', encoding='utf-8') as f:
                for line in converted_lines:
                    f.write(line + '\n')
            print(f"✓ 转换完成: {len(converted_lines)} 个目标")
        else:
            print(f"✗ 没有有效目标，删除文件: {txt_file}")
            os.remove(txt_file)

def main():
    print("将关键点标注转换为目标检测格式...")
    
    # 处理训练集标签
    train_labels_dir = "yolo_dataset/labels/train"
    if os.path.exists(train_labels_dir):
        print("处理训练集标签...")
        process_labels_directory(train_labels_dir)
    
    # 处理验证集标签
    val_labels_dir = "yolo_dataset/labels/val"
    if os.path.exists(val_labels_dir):
        print("处理验证集标签...")
        process_labels_directory(val_labels_dir)
    
    # 删除缓存文件，让YOLO重新生成
    cache_files = [
        "yolo_dataset/labels/train.cache",
        "yolo_dataset/labels/val.cache"
    ]
    
    for cache_file in cache_files:
        if os.path.exists(cache_file):
            os.remove(cache_file)
            print(f"删除缓存文件: {cache_file}")
    
    # 更新data.yaml文件，移除关键点配置
    data_yaml_path = "data.yaml"
    if os.path.exists(data_yaml_path):
        with open(data_yaml_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 移除关键点相关配置
        new_lines = []
        for line in lines:
            if not line.strip().startswith(('kpt_shape:', 'flip_idx:')):
                new_lines.append(line)
        
        with open(data_yaml_path, 'w', encoding='utf-8') as f:
            f.writelines(new_lines)
        
        print("已更新data.yaml文件，移除关键点配置")
    
    print("转换完成！现在可以使用普通的YOLO目标检测模型进行训练了。")

if __name__ == "__main__":
    main()
